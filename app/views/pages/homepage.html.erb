<div>
  <%#= render BlockBuilder.component('gallery002') %>

  <% if @page.blocks.any? %>
    <%= render PageViewBuilder.build_from_page(@page) %>
  <% end %>

  <%#= render BlockBuilder.build(@page.blocks.first) %>
</div>


<section class="py-16 lg:py-24 bg-gradient-to-br from-base-100 to-base-200">
  <div class="container mx-auto px-4">
    <!-- Header -->
    <div class="text-center mb-12">
      <div class="flex justify-center mb-6">
        <div class="w-16 h-16 bg-gradient-to-br from-pink-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
          <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
          </svg>
        </div>
      </div>

      <h2 class="text-4xl md:text-5xl font-bold text-base-content mb-4">
        Sledujte nás na
        <span class="bg-gradient-to-r from-pink-500 to-purple-600 bg-clip-text text-transparent">
          Instagramu
        </span>
      </h2>

      <p class="text-xl text-base-content/70 max-w-2xl mx-auto mb-8">
        Denní dávka inspirace, zákulisí a nejnovějších trendů z našeho beauty prostoru
      </p>

      <a href="https://www.instagram.com/oficina_2.0/" target="_blank" style="background: #7d9e8b; color: #fff" class="btn btn-soft-accent rounded-box btn-lg px-8 py-4 text-lg transition-all duration-300 transform hover:-translate-y-1">
        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
        </svg>
        Sledovat @oficina_2.0
      </a>
    </div>

    <!-- Instagram Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
      <% @instagram_media.each do |media| %>
        <div class="group relative overflow-hidden rounded-2xl bg-base-200 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
          <% if media.image.attached? %>
            <div class="aspect-square overflow-hidden">
              <%= image_tag media.image,
                            class: "w-full h-full object-cover group-hover:scale-110 transition-transform duration-500",
                            loading: :lazy %>
            </div>
          <% end %>

          <!-- Overlay -->
          <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent transition-opacity duration-300">
            <div class="absolute bottom-0 left-0 right-0 p-6">
              <% if media.published_at %>
                <div class="text-white/80 text-sm mb-2">
                  <%= l media.published_at, format: :date %>
                </div>
              <% end %>

              <% if media.caption.present? %>
                <p class="text-white text-sm leading-relaxed line-clamp-3">
                  <%= media.caption.truncate(120) %>
                </p>
              <% end %>

              <% if media.url.present? %>
                <a href="<%= media.url %>" target="_blank" class="inline-flex items-center mt-3 text-white hover:text-primary transition-colors">
                  <span class="text-sm font-medium">Zobrazit na Instagramu</span>
                  <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                  </svg>
                </a>
              <% end %>
            </div>
          </div>

          <!-- Instagram Icon -->
          <div class="absolute top-4 right-4 w-8 h-8 bg-white/90 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <svg class="w-5 h-5 text-pink-500" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
            </svg>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</section>

<section class="text-gray-600 body-font relative flex flex-col sm:flex-row">
  <div class="sm:absolute inset-0 bg-gray-300">
    <div data-controller="map" data-map-url="<%= current_tenant.map_url %>" style="min-height: 250px; height: 100%; width: 100%;"></div>
  </div>
  <div class="w-full order-first sm:order-last sm:container sm:px-5 py-5 sm:py-12 mx-auto flex bg-white border">
    <div class="w-full lg:w-1/3 md:w-1/2 p-8 flex flex-col md:ml-auto relative z-10 sm:shadow-md sm:bg-white">
      <span class="text-gray-900 text-lg mb-1 font-medium title-font">Kontakt</span>
      <div class="leading-relaxed mb-5 text-gray-600">
        <strong>Oficina 2.0 - Druhé patro</strong><br>
        <%= current_tenant.address %>
        <br>
        <%= current_tenant.postal_code %> <%= current_tenant.city %> <br />
        tel: <%= current_tenant.phone %>
      </div>

      <span class="text-black text-lg mb-1 font-medium title-font">Otevírací doba</span>
      <% if current_tenant.opening_hours_text.present? %>
        <%= current_tenant.opening_hours_text %>
      <% else %>
        <table class="w-full">
          <tbody>
          <% weekdays.each_with_index do |day, index| %>
            <% date = Date.today.beginning_of_week + index %>

            <%= next if @specific_dates[date].nil? && @generic_days[day[:value]].nil? %>
            <tr class="text-black">
              <td class="<%= Date.today == date ? "font-bold" : "" %>"><%= day[:label] %>:</td>
              <td class="<%= Date.today == date ? "font-bold" : "" %>">
                <% if @specific_dates[date] %>
                  <% @specific_dates[date].each do |opening_hour| %>
                    <p><%= opening_hour.opening_hour.strftime("%H:%M") %> - <%= opening_hour.closing_hour.strftime("%H:%M") %></p>
                  <% end %>
                <% elsif @generic_days[day[:value]] %>
                  <% if @generic_days[day[:value]].opening_hour.present? &&  @generic_days[day[:value]].closing_hour.present? %>
                    <p><%= @generic_days[day[:value]].opening_hour.strftime("%H:%M") %> - <%= @generic_days[day[:value]].closing_hour.strftime("%H:%M") %></p>
                  <% else %>
                    Dle objednání
                  <% end %>
                <% else %>
                  <p>Zavřeno</p>
                <% end %>
              </td>
            </tr>
          <% end %>
          </tbody>
        </table>
      <% end %>
    </div>
  </div>
</section>


<section class="hidden text-gray-600 body-font relative flex flex-col sm:flex-row">
  <div class="sm:absolute inset-0 bg-gray-300">
    <div data-controller="map" data-map-url="<%= current_tenant.map_url %>" style="min-height: 250px; height: 100%; width: 100%;"></div>
  </div>
  <div class="w-full order-first sm:order-last sm:container sm:px-5 py-5 sm:py-12 mx-auto flex bg-white border">
    <div class="w-full lg:w-1/3 md:w-1/2 p-8 flex flex-col md:ml-auto relative z-10 sm:shadow-md sm:bg-white">
      <span class="text-gray-900 text-lg mb-1 font-medium title-font">Kontakt</span>
      <div class="leading-relaxed mb-5 text-gray-600">
        <strong>Oficina 2.0 - Druhé patro</strong><br>
        <%= current_tenant.address %>
        <br>
        <%= current_tenant.postal_code %> <%= current_tenant.city %> <br />
        tel: <%= current_tenant.phone %>
      </div>

      <span class="text-black text-lg mb-1 font-medium title-font">Otevírací doba</span>
      <% if current_tenant.opening_hours_text.present? %>
        <%= current_tenant.opening_hours_text %>
      <% else %>
        <table class="w-full">
          <tbody>
          <% weekdays.each_with_index do |day, index| %>
            <% date = Date.today.beginning_of_week + index %>

            <%= next if @specific_dates[date].nil? && @generic_days[day[:value]].nil? %>
            <tr class="text-black">
              <td class="<%= Date.today == date ? "font-bold" : "" %>"><%= day[:label] %>:</td>
              <td class="<%= Date.today == date ? "font-bold" : "" %>">
                <% if @specific_dates[date] %>
                  <% @specific_dates[date].each do |opening_hour| %>
                    <p><%= opening_hour.opening_hour.strftime("%H:%M") %> - <%= opening_hour.closing_hour.strftime("%H:%M") %></p>
                  <% end %>
                <% elsif @generic_days[day[:value]] %>
                  <% if @generic_days[day[:value]].opening_hour.present? &&  @generic_days[day[:value]].closing_hour.present? %>
                  <p><%= @generic_days[day[:value]].opening_hour.strftime("%H:%M") %> - <%= @generic_days[day[:value]].closing_hour.strftime("%H:%M") %></p>
                    <% else %>
                      Dle objednání
                    <% end %>
                <% else %>
                  <p>Zavřeno</p>
                <% end %>
              </td>
            </tr>
          <% end %>
          </tbody>
        </table>
      <% end %>
    </div>
  </div>
</section>