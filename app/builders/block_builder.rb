class BlockBuilder
  def self.component(source, context: :frontend)
    build(source, context: context).component
  end

  def self.build(source, context: :frontend)
    config = ConfigAdapter.for(source)

    block_id = source.is_a?(Block) ? source.id : Digest::MD5.hexdigest(config.name.to_s)[0,8]

    BlockPresenter.new(
      id: block_id,
      name: config.name,
      context: context,
      options: config.options || {}, # TOTO NYNÍ OBSAHUJE VŠECHNY *_layer_attributes
      controls: build_controls(config.controls_data, context),
      media: build_media(config.media_data), # media_data nyní poskytuje jen typ a itemy
      pricing_id: config.pricing_id,
      pricing_options: config.pricing_options || {}, # Zvažte přesun i tohoto do options
      background_image: config.background_image_attachment,
      background_image_mobile: config.background_image_mobile_attachment
    )
  end

  def self.build_controls(controls_data, context) # <PERSON><PERSON><PERSON>dn<PERSON> 'controls'
    Array(controls_data).map do |control_config|   # Přejmenováno 'control' na 'control_config' pro srozumitelnost
      control_class = ControlRegistry.class_for(control_config[:type])

      control_class.new(control_config, context)
    end
  end

  def self.build_media(media_config)
    return MediaPresenter.new if media_config.blank?

    media_object_options = media_config.except(:items_payload)
    raw_items_data = media_config[:items_payload]

    MediaPresenter.new(options: media_object_options, items_payload: raw_items_data)
  end

  def self.blocks_by_type(type)
    Rails.application.config.x.components_by_type[type].map do |key, name|
      build(key.to_s, context: :frontend)
    end
  end
end
