class PageViewBuilder
  def initialize(page_object)
    @page_object = page_object
  end

  def render_in(view_context, &block)
    @page_object.blocks.map do |component|
      component.render_in(view_context)
    end.join.html_safe
  end

  def self.build_from_page(page, context: :frontend)
    blocks = page.blocks.visible.order(:position).map do |block|
      BlockBuilder.component(block, context: context)
    end

    page_object = PagePresenter.new(
      name: page.title,
      blocks: blocks
    )

    new(page_object)
  end
end
