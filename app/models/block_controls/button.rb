# == Schema Information
#
# Table name: block_controls
#
#  id         :bigint           not null, primary key
#  classes    :jsonb
#  container  :string
#  content    :jsonb
#  locale     :string           default("cs"), not null
#  options    :jsonb
#  position   :integer          default(0)
#  styles     :jsonb
#  text       :text
#  type       :string
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  block_id   :bigint
#
# Indexes
#
#  index_block_controls_on_block_id             (block_id)
#  index_block_controls_on_block_id_and_locale  (block_id,locale)
#
# Foreign Keys
#
#  fk_rails_...  (block_id => blocks.id)
#
class BlockControls::Button < BlockControl
  store_accessor :options, :primary_button_text, :primary_button_link, :primary_button_email, :primary_button_phone, :secondary_button_text, :secondary_button_link, :primary_button_page_id,
                  :secondary_button_page_id, :secondary_button_email, :secondary_button_phone, :primary_button_link_type, :secondary_button_link_type


  def self.permitted_attributes
    [:id, :pre_header, :primary_button_text, :primary_button_link, :primary_button_email, :primary_button_phone,
     :secondary_button_text, :secondary_button_link, :secondary_button_email, :secondary_button_phone,
     :primary_button_page_id, :secondary_button_page_id,
     :primary_button_link_type, :secondary_button_link_type, classes: {}]
  end

  def primary_page
    return nil unless primary_button_page_id.present?

    Page.find_by_id(primary_button_page_id)
  end

  def secondary_page
    return nil unless secondary_button_page_id.present?

    Page.find_by_id(secondary_button_page_id)
  end

  def self.sti_name
    'Button'
  end
end
